{".class": "MypyFile", "_fullname": "re", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "A": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.A", "name": "A", "type": "re.RegexFlag"}}, "ASCII": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.ASCII", "name": "ASCII", "type": "re.RegexFlag"}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AnyStr": {".class": "SymbolTableNode", "cross_ref": "typing.AnyStr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DEBUG": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.DEBUG", "name": "DEBUG", "type": "re.RegexFlag"}}, "DOTALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.DOTALL", "name": "DOTALL", "type": "re.RegexFlag"}}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GenericAlias": {".class": "SymbolTableNode", "cross_ref": "types.GenericAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "I": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.I", "name": "I", "type": "re.RegexFlag"}}, "IGNORECASE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.IGNORECASE", "name": "IGNORECASE", "type": "re.RegexFlag"}}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "L": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.L", "name": "L", "type": "re.RegexFlag"}}, "LOCALE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.LOCALE", "name": "LOCALE", "type": "re.RegexFlag"}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "M": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.M", "name": "M", "type": "re.RegexFlag"}}, "MULTILINE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.MULTILINE", "name": "MULTILINE", "type": "re.RegexFlag"}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Match": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "re.Match", "name": "Match", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "re.Match", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "re", "mro": ["re.Match", "builtins.object"], "names": {".class": "SymbolTable", "__class_getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class"], "fullname": "re.Match.__class_getitem__", "name": "__class_getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__class_getitem__ of Match", "ret_type": "types.GenericAlias", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__copy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "re.Match.__copy__", "name": "__copy__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__copy__ of Match", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__deepcopy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "re.Match.__deepcopy__", "name": "__deepcopy__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__deepcopy__ of Match", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.Match.__getitem__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Match.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of Match", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Match.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of Match", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Match.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of Match", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Match.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of Match", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of Match", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of Match", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "re.Match.end", "name": "end", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "end of Match", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "endpos": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "re.Match.endpos", "name": "endpos", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endpos of Match", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "re.Match.endpos", "name": "endpos", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endpos of Match", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "expand": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.Match.expand", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "template"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Match.expand", "name": "expand", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "template"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand of Match", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Match.expand", "name": "expand", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "template"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand of Match", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "template"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Match.expand", "name": "expand", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "template"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, "typing_extensions.Buffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand of Match", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Match.expand", "name": "expand", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "template"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, "typing_extensions.Buffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand of Match", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "template"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Match.expand", "name": "expand", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "template"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand of Match", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Match.expand", "name": "expand", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "template"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand of Match", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "template"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand of Match", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "template"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, "typing_extensions.Buffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand of Match", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "template"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand of Match", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.Match.group", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Match.group", "name": "group", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group of Match", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Match.group", "name": "group", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group of Match", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Match.group", "name": "group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group of Match", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Match.group", "name": "group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group of Match", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2], "arg_names": [null, null, null, "groups"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Match.group", "name": "group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2], "arg_names": [null, null, null, "groups"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group of Match", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Match.group", "name": "group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2], "arg_names": [null, null, null, "groups"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group of Match", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group of Match", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group of Match", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 2], "arg_names": [null, null, null, "groups"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group of Match", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "groupdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.Match.groupdict", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Match.groupdict", "name": "groupdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "groupdict of Match", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Match.groupdict", "name": "groupdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "groupdict of Match", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Match.groupdict", "name": "groupdict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "default"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "id": -1, "name": "_T", "namespace": "re.Match.groupdict", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "groupdict of Match", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "id": -1, "name": "_T", "namespace": "re.Match.groupdict", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "id": -1, "name": "_T", "namespace": "re.Match.groupdict", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Match.groupdict", "name": "groupdict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "default"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "id": -1, "name": "_T", "namespace": "re.Match.groupdict", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "groupdict of Match", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "id": -1, "name": "_T", "namespace": "re.Match.groupdict", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "id": -1, "name": "_T", "namespace": "re.Match.groupdict", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "groupdict of Match", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "default"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "id": -1, "name": "_T", "namespace": "re.Match.groupdict", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "groupdict of Match", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "id": -1, "name": "_T", "namespace": "re.Match.groupdict", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "id": -1, "name": "_T", "namespace": "re.Match.groupdict", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.Match.groups", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Match.groups", "name": "groups", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "groups of Match", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Match.groups", "name": "groups", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "groups of Match", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Match.groups", "name": "groups", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "default"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "id": -1, "name": "_T", "namespace": "re.Match.groups", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "groups of Match", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "id": -1, "name": "_T", "namespace": "re.Match.groups", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "id": -1, "name": "_T", "namespace": "re.Match.groups", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Match.groups", "name": "groups", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "default"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "id": -1, "name": "_T", "namespace": "re.Match.groups", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "groups of Match", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "id": -1, "name": "_T", "namespace": "re.Match.groups", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "id": -1, "name": "_T", "namespace": "re.Match.groups", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "groups of Match", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "default"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "id": -1, "name": "_T", "namespace": "re.Match.groups", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "groups of Match", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "id": -1, "name": "_T", "namespace": "re.Match.groups", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "id": -1, "name": "_T", "namespace": "re.Match.groups", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "lastgroup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "re.Match.lastgroup", "name": "lastgroup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lastgroup of Match", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "re.Match.lastgroup", "name": "lastgroup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lastgroup of Match", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "lastindex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "re.Match.lastindex", "name": "lastindex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lastindex of Match", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "re.Match.lastindex", "name": "lastindex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lastindex of Match", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pos": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "re.Match.pos", "name": "pos", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pos of Match", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "re.Match.pos", "name": "pos", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pos of Match", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "re": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "re.Match.re", "name": "re", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "re of Match", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "re.Match.re", "name": "re", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "re of Match", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "regs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "re.Match.regs", "name": "regs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "regs of Match", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "re.Match.regs", "name": "regs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "regs of Match", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "span": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "re.Match.span", "name": "span", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "span of Match", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "re.Match.start", "name": "start", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start of Match", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "re.Match.string", "name": "string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "string of Match", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "re.Match.string", "name": "string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "string of Match", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "re.Match", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": ["AnyStr"], "typeddict_type": null}}, "MaybeNone": {".class": "SymbolTableNode", "cross_ref": "_typeshed.MaybeNone", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NOFLAG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.NOFLAG", "name": "NOFLAG", "type": "re.RegexFlag"}}, "Pattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "<PERSON><PERSON>", "name": "Pattern", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "re", "mro": ["<PERSON><PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "__class_getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class"], "fullname": "re.Pattern.__class_getitem__", "name": "__class_getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__class_getitem__ of Pattern", "ret_type": "types.GenericAlias", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__copy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "re.<PERSON><PERSON>.__copy__", "name": "__copy__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__copy__ of <PERSON><PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__deepcopy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "re.<PERSON>tern.__deepcopy__", "name": "__deepcopy__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__deepcopy__ of <PERSON><PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "re.Pattern.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of <PERSON><PERSON>", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "re.<PERSON>tern.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of Pattern", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "findall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.Pattern.findall", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.findall", "name": "findall", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findall of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.findall", "name": "findall", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findall of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.findall", "name": "findall", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "typing_extensions.Buffer", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findall of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.findall", "name": "findall", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "typing_extensions.Buffer", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findall of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.findall", "name": "findall", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findall of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.findall", "name": "findall", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findall of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findall of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "typing_extensions.Buffer", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findall of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findall of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "finditer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.Pattern.finditer", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.finditer", "name": "finditer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finditer of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.finditer", "name": "finditer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finditer of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.finditer", "name": "finditer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "typing_extensions.Buffer", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finditer of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.finditer", "name": "finditer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "typing_extensions.Buffer", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finditer of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.finditer", "name": "finditer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finditer of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.finditer", "name": "finditer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finditer of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finditer of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "typing_extensions.Buffer", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finditer of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finditer of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "re.Pattern.flags", "name": "flags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flags of Pattern", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "re.Pattern.flags", "name": "flags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flags of Pattern", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "fullmatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.Pattern.fullmatch", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.fullmatch", "name": "fullmatch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fullmatch of Pattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.fullmatch", "name": "fullmatch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fullmatch of Pattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.fullmatch", "name": "fullmatch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "typing_extensions.Buffer", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fullmatch of Pattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.fullmatch", "name": "fullmatch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "typing_extensions.Buffer", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fullmatch of Pattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.fullmatch", "name": "fullmatch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fullmatch of Pattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.fullmatch", "name": "fullmatch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fullmatch of Pattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fullmatch of Pattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "typing_extensions.Buffer", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fullmatch of Pattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fullmatch of Pattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "groupindex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "re.Pattern.groupindex", "name": "groupindex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "groupindex of Pattern", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "re.Pattern.groupindex", "name": "groupindex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "groupindex of Pattern", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "re.Pattern.groups", "name": "groups", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "groups of Pattern", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "re.Pattern.groups", "name": "groups", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "groups of Pattern", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.Pattern.match", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of Pattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of Pattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "typing_extensions.Buffer", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of Pattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "typing_extensions.Buffer", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of Pattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of Pattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of Pattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of Pattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "typing_extensions.Buffer", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of Pattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of Pattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "re.Pattern.pattern", "name": "pattern", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pattern of Pattern", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "re.Pattern.pattern", "name": "pattern", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pattern of Pattern", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "search": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.Pattern.search", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.search", "name": "search", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.search", "name": "search", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.search", "name": "search", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "typing_extensions.Buffer", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.search", "name": "search", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "typing_extensions.Buffer", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.search", "name": "search", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.search", "name": "search", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "typing_extensions.Buffer", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "string", "pos", "endpos"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "split": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.Pattern.split", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "string", "maxsplit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "string", "maxsplit"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "string", "maxsplit"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "string", "maxsplit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "string", "maxsplit"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "typing_extensions.Buffer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "string", "maxsplit"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "typing_extensions.Buffer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "string", "maxsplit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "string", "maxsplit"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "string", "maxsplit"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "string", "maxsplit"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "string", "maxsplit"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "typing_extensions.Buffer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "string", "maxsplit"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split of Pattern", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "sub": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.Pattern.sub", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.sub", "name": "sub", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sub of Pattern", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.sub", "name": "sub", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sub of Pattern", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.sub", "name": "sub", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "UnionType", "items": ["typing_extensions.Buffer", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "typing_extensions.Buffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sub of Pattern", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.sub", "name": "sub", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "UnionType", "items": ["typing_extensions.Buffer", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "typing_extensions.Buffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sub of Pattern", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.sub", "name": "sub", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sub of Pattern", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.sub", "name": "sub", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sub of Pattern", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sub of Pattern", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "UnionType", "items": ["typing_extensions.Buffer", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "typing_extensions.Buffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sub of Pattern", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sub of Pattern", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "subn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.Pattern.subn", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.subn", "name": "subn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subn of Pattern", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.subn", "name": "subn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subn of Pattern", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.subn", "name": "subn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "UnionType", "items": ["typing_extensions.Buffer", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "typing_extensions.Buffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subn of Pattern", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.subn", "name": "subn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "UnionType", "items": ["typing_extensions.Buffer", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "typing_extensions.Buffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subn of Pattern", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.Pattern.subn", "name": "subn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subn of Pattern", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.Pattern.subn", "name": "subn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subn of Pattern", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subn of Pattern", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "UnionType", "items": ["typing_extensions.Buffer", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "typing_extensions.Buffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subn of Pattern", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repl", "string", "count"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subn of Pattern", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": 1, "name": "AnyStr", "namespace": "<PERSON><PERSON>", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": ["AnyStr"], "typeddict_type": null}}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RegexFlag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntFlag"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "re.RegexFlag", "name": "RegexFlag", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "re.RegexFlag", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "re", "mro": ["re.RegexFlag", "enum.IntFlag", "builtins.int", "enum.ReprEnum", "enum.Flag", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "A": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "re.RegexFlag.A", "name": "A", "type": "builtins.int"}}, "ASCII": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "re.RegexFlag.ASCII", "name": "ASCII", "type": "builtins.int"}}, "DEBUG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "re.RegexFlag.DEBUG", "name": "DEBUG", "type": "builtins.int"}}, "DOTALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "re.RegexFlag.DOTALL", "name": "DOTALL", "type": "builtins.int"}}, "I": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "re.RegexFlag.I", "name": "I", "type": "builtins.int"}}, "IGNORECASE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "re.RegexFlag.IGNORECASE", "name": "IGNORECASE", "type": "builtins.int"}}, "L": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "re.RegexFlag.L", "name": "L", "type": "builtins.int"}}, "LOCALE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "re.RegexFlag.LOCALE", "name": "LOCALE", "type": "builtins.int"}}, "M": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "re.RegexFlag.M", "name": "M", "type": "builtins.int"}}, "MULTILINE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "re.RegexFlag.MULTILINE", "name": "MULTILINE", "type": "builtins.int"}}, "NOFLAG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "re.RegexFlag.NOFLAG", "name": "NOFLAG", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "S": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "re.RegexFlag.S", "name": "S", "type": "builtins.int"}}, "T": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "re.RegexFlag.T", "name": "T", "type": "builtins.int"}}, "TEMPLATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "re.RegexFlag.TEMPLATE", "name": "TEMPLATE", "type": "builtins.int"}}, "U": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "re.RegexFlag.U", "name": "U", "type": "builtins.int"}}, "UNICODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "re.RegexFlag.UNICODE", "name": "UNICODE", "type": "builtins.int"}}, "VERBOSE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "re.RegexFlag.VERBOSE", "name": "VERBOSE", "type": "builtins.int"}}, "X": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "re.RegexFlag.X", "name": "X", "type": "builtins.int"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.S", "name": "S", "type": "re.RegexFlag"}}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.T", "name": "T", "type": "re.RegexFlag"}}, "TEMPLATE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.TEMPLATE", "name": "TEMPLATE", "type": "re.RegexFlag"}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "U": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.U", "name": "U", "type": "re.RegexFlag"}}, "UNICODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.UNICODE", "name": "UNICODE", "type": "re.RegexFlag"}}, "VERBOSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.VERBOSE", "name": "VERBOSE", "type": "re.RegexFlag"}}, "X": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.X", "name": "X", "type": "re.RegexFlag"}}, "_FlagsType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "re._FlagsType", "line": 247, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.int", "re.RegexFlag"], "uses_pep604_syntax": true}}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "re._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "re.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "re.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "re.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "re.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "re.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "re.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "re.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "compile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.compile", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["pattern", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.compile", "name": "compile", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["pattern", "flags"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.compile#0", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compile", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.compile#0", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.compile#0", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.compile", "name": "compile", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["pattern", "flags"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.compile#0", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compile", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.compile#0", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.compile#0", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["pattern", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.compile", "name": "compile", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["pattern", "flags"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.compile", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compile", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.compile", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.compile", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.compile", "name": "compile", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["pattern", "flags"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.compile", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compile", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.compile", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.compile", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["pattern", "flags"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.compile#0", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compile", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.compile#0", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.compile#0", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["pattern", "flags"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.compile", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compile", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.compile", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.compile", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}]}}}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "re.error", "name": "error", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "re.error", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "re", "mro": ["re.error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "msg", "pattern", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "re.error.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "msg", "pattern", "pos"], "arg_types": ["re.error", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of error", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "colno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "re.error.colno", "name": "colno", "type": "builtins.int"}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "re.error.lineno", "name": "lineno", "type": "builtins.int"}}, "msg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "re.error.msg", "name": "msg", "type": "builtins.str"}}, "pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "re.error.pattern", "name": "pattern", "type": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "pos": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "re.error.pos", "name": "pos", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "escape": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pattern"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "re.escape", "name": "escape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pattern"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.escape", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "escape", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.escape", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.escape", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "findall": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.findall", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.findall", "name": "findall", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findall", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.findall", "name": "findall", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findall", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.findall", "name": "findall", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findall", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.findall", "name": "findall", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findall", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findall", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findall", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "finditer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.finditer", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.finditer", "name": "finditer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finditer", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.finditer", "name": "finditer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finditer", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.finditer", "name": "finditer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finditer", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.finditer", "name": "finditer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finditer", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finditer", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finditer", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "fullmatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.fullmatch", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.fullmatch", "name": "fullmatch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fullmatch", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.fullmatch", "name": "fullmatch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fullmatch", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.fullmatch", "name": "fullmatch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fullmatch", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.fullmatch", "name": "fullmatch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fullmatch", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fullmatch", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fullmatch", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "match": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.match", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "purge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "re.purge", "name": "purge", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "purge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "search": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.search", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.search", "name": "search", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.search", "name": "search", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.search", "name": "search", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.search", "name": "search", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pattern", "string", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "split": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.split", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["pattern", "string", "maxsplit", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["pattern", "string", "maxsplit", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["pattern", "string", "maxsplit", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["pattern", "string", "maxsplit", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["pattern", "string", "maxsplit", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["pattern", "string", "maxsplit", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["pattern", "string", "maxsplit", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["pattern", "string", "maxsplit", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "sre_compile": {".class": "SymbolTableNode", "cross_ref": "sre_compile", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sre_constants": {".class": "SymbolTableNode", "cross_ref": "sre_constants", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.sub", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["pattern", "repl", "string", "count", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.sub", "name": "sub", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["pattern", "repl", "string", "count", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sub", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.sub", "name": "sub", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["pattern", "repl", "string", "count", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sub", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["pattern", "repl", "string", "count", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.sub", "name": "sub", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["pattern", "repl", "string", "count", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing_extensions.Buffer", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "typing_extensions.Buffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sub", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.sub", "name": "sub", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["pattern", "repl", "string", "count", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing_extensions.Buffer", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "typing_extensions.Buffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sub", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["pattern", "repl", "string", "count", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sub", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["pattern", "repl", "string", "count", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing_extensions.Buffer", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "typing_extensions.Buffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sub", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "subn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "re.subn", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["pattern", "repl", "string", "count", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.subn", "name": "subn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["pattern", "repl", "string", "count", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subn", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.subn", "name": "subn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["pattern", "repl", "string", "count", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subn", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["pattern", "repl", "string", "count", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "re.subn", "name": "subn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["pattern", "repl", "string", "count", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing_extensions.Buffer", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "typing_extensions.Buffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subn", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "re.subn", "name": "subn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["pattern", "repl", "string", "count", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing_extensions.Buffer", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "typing_extensions.Buffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subn", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["pattern", "repl", "string", "count", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subn", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["pattern", "repl", "string", "count", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing_extensions.Buffer", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "typing_extensions.Buffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "typing_extensions.Buffer", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subn", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["pattern", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "re.template", "name": "template", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["pattern", "flags"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.template", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.template", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "re._FlagsType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "template", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.template", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "re.template", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}}, "path": "/Users/<USER>/.vscode/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/re.pyi"}