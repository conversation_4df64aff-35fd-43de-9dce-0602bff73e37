{"data_mtime": 1753084102, "dep_lines": [6, 1, 2, 3, 5, 7, 8, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "_typeshed", "sys", "types", "builtins", "typing", "typing_extensions", "_frozen_importlib", "abc"], "hash": "8d5c0cd8657b5fa3d633d49b0b7b2ea3df2083b0", "id": "enum", "ignore_all": true, "interface_hash": "6ee1a7e94c7c03ccde3b2259d68700ffc3801ded", "mtime": 1750496964, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.vscode/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/enum.pyi", "plugin_data": null, "size": 12074, "suppressed": [], "version_id": "1.15.0"}