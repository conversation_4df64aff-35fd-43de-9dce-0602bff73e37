{"data_mtime": 1753085613, "dep_lines": [24, 25, 32, 18, 20, 21, 22, 24, 1, 1, 1, 1, 1, 52, 53, 55, 56, 29, 30], "dep_prios": [10, 5, 25, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 5, 5, 5, 5, 25, 25], "dependencies": ["PIL.Image", "PIL._util", "PIL.ImageFile", "__future__", "sys", "io", "typing", "PIL", "builtins", "_frozen_importlib", "_typeshed", "abc", "types"], "hash": "ac5550aae7c624b1875fbb0c6adddb54455947cd", "id": "PIL.ImageQt", "ignore_all": true, "interface_hash": "32ef36613882d7c3ab3c1d80717e7cbcacd60d6c", "mtime": 1753085295, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Developers/Tinkering/portfolio/.venv/lib/python3.11/site-packages/PIL/ImageQt.py", "plugin_data": null, "size": 6841, "suppressed": ["PyQt6.QtCore", "PyQt6.QtGui", "PySide6.QtCore", "PySide6.QtGui", "PyQt6", "PySide6"], "version_id": "1.15.0"}