{"data_mtime": 1753085613, "dep_lines": [11, 12, 1, 3, 4, 5, 6, 7, 9, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 10, 10, 10, 5, 10, 5, 30, 30], "dependencies": ["PIL.Image", "PIL._deprecate", "__future__", "collections", "os", "sys", "warnings", "typing", "PIL", "builtins", "_frozen_importlib", "abc"], "hash": "cc057f3b7b863b7ae8e812414429734a7f05596f", "id": "PIL.features", "ignore_all": true, "interface_hash": "3325bfd2da92bc57574df14ee0480dc5a58b7d77", "mtime": 1753085295, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Developers/Tinkering/portfolio/.venv/lib/python3.11/site-packages/PIL/features.py", "plugin_data": null, "size": 11479, "suppressed": [], "version_id": "1.15.0"}