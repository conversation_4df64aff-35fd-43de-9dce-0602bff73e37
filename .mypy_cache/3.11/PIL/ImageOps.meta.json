{"data_mtime": 1753085613, "dep_lines": [24, 27, 27, 27, 19, 21, 22, 23, 25, 27, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30], "dependencies": ["collections.abc", "PIL.ExifTags", "PIL.Image", "PIL.ImagePalette", "__future__", "functools", "operator", "re", "typing", "PIL", "builtins", "_frozen_importlib", "abc", "enum", "types"], "hash": "4bcc667d22004c5e2b5d7abbc6cfc4e1a9672d79", "id": "PIL.ImageOps", "ignore_all": true, "interface_hash": "04b2af569caa0d34c029066e22a890552a2517a2", "mtime": 1753085295, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Developers/Tinkering/portfolio/.venv/lib/python3.11/site-packages/PIL/ImageOps.py", "plugin_data": null, "size": 25525, "suppressed": [], "version_id": "1.15.0"}