{"data_mtime": 1753085613, "dep_lines": [21, 27, 28, 596, 17, 19, 20, 22, 23, 27, 1, 1, 421], "dep_prios": [5, 25, 25, 20, 5, 10, 10, 5, 5, 20, 5, 30, 20], "dependencies": ["collections.abc", "PIL._imaging", "PIL._typing", "PIL.Image", "__future__", "abc", "functools", "types", "typing", "PIL", "builtins", "_frozen_importlib"], "hash": "430c356953d5dc2ae8b0c78cdc7e6daf14e0719c", "id": "PIL.ImageFilter", "ignore_all": true, "interface_hash": "6788dea0d78a3cc8dcfb1d1b8df84cfadaa9f564", "mtime": 1753085295, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Developers/Tinkering/portfolio/.venv/lib/python3.11/site-packages/PIL/ImageFilter.py", "plugin_data": null, "size": 18671, "suppressed": ["numpy"], "version_id": "1.15.0"}