{".class": "MypyFile", "_fullname": "PIL.ImageFont", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Axis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL.ImageFont.Axis", "name": "Axis", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.Axis", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PIL.ImageFont", "mro": ["PIL.ImageFont.Axis", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["minimum", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["default", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["maximum", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["name", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": ["default", "maximum", "minimum", "name"]}}}, "BinaryIO": {".class": "SymbolTableNode", "cross_ref": "typing.BinaryIO", "kind": "Gdef"}, "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef"}, "DeferredError": {".class": "SymbolTableNode", "cross_ref": "PIL._util.DeferredError", "kind": "Gdef"}, "Font": {".class": "SymbolTableNode", "cross_ref": "PIL._imagingft.Font", "kind": "Gdef"}, "FreeTypeFont": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL.ImageFont.FreeTypeFont", "name": "FreeTypeFont", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.FreeTypeFont", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PIL.ImageFont", "mro": ["PIL.ImageFont.FreeTypeFont", "builtins.object"], "names": {".class": "SymbolTable", "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.FreeTypeFont.__getstate__", "name": "__getstate__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL.ImageFont.FreeTypeFont"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getstate__ of FreeTypeFont", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "font", "size", "index", "encoding", "layout_engine"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.FreeTypeFont.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "font", "size", "index", "encoding", "layout_engine"], "arg_types": ["PIL.ImageFont.FreeTypeFont", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._typing.StrOrBytesPath"}, "typing.BinaryIO"], "uses_pep604_syntax": true}, "builtins.float", "builtins.int", "builtins.str", {".class": "UnionType", "items": ["PIL.ImageFont.Layout", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FreeTypeFont", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.FreeTypeFont.__setstate__", "name": "__setstate__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["PIL.ImageFont.FreeTypeFont", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setstate__ of FreeTypeFont", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_last_variation_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageFont.FreeTypeFont._last_variation_index", "name": "_last_variation_index", "type": "builtins.int"}}, "encoding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageFont.FreeTypeFont.encoding", "name": "encoding", "type": "builtins.str"}}, "font": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "PIL.ImageFont.FreeTypeFont.font", "name": "font", "type": "PIL._imagingft.Font"}}, "font_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "PIL.ImageFont.FreeTypeFont.font_bytes", "name": "font_bytes", "type": "builtins.bytes"}}, "font_variant": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "font", "size", "index", "encoding", "layout_engine"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.FreeTypeFont.font_variant", "name": "font_variant", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "font", "size", "index", "encoding", "layout_engine"], "arg_types": ["PIL.ImageFont.FreeTypeFont", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._typing.StrOrBytesPath"}, "typing.BinaryIO", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["PIL.ImageFont.Layout", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "font_variant of FreeTypeFont", "ret_type": "PIL.ImageFont.FreeTypeFont", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_variation_axes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.FreeTypeFont.get_variation_axes", "name": "get_variation_axes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL.ImageFont.FreeTypeFont"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_variation_axes of FreeTypeFont", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageFont.Axis"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_variation_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.FreeTypeFont.get_variation_names", "name": "get_variation_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL.ImageFont.FreeTypeFont"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_variation_names of FreeTypeFont", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getbbox": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "text", "mode", "direction", "features", "language", "stroke_width", "anchor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.FreeTypeFont.getbbox", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "text", "mode", "direction", "features", "language", "stroke_width", "anchor"], "arg_types": ["PIL.ImageFont.FreeTypeFont", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getbbox of FreeTypeFont", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getlength": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "text", "mode", "direction", "features", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.FreeTypeFont.getlength", "name": "getlength", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "text", "mode", "direction", "features", "language"], "arg_types": ["PIL.ImageFont.FreeTypeFont", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getlength of FreeTypeFont", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getmask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "text", "mode", "direction", "features", "language", "stroke_width", "anchor", "ink", "start"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.FreeTypeFont.getmask", "name": "getmask", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "text", "mode", "direction", "features", "language", "stroke_width", "anchor", "ink", "start"], "arg_types": ["PIL.ImageFont.FreeTypeFont", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getmask of FreeTypeFont", "ret_type": "PIL._imaging.ImagingCore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getmask2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 2, 4], "arg_names": ["self", "text", "mode", "direction", "features", "language", "stroke_width", "anchor", "ink", "start", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.FreeTypeFont.getmask2", "name": "getmask2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 2, 4], "arg_names": ["self", "text", "mode", "direction", "features", "language", "stroke_width", "anchor", "ink", "start", "args", "kwargs"], "arg_types": ["PIL.ImageFont.FreeTypeFont", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getmask2 of FreeTypeFont", "ret_type": {".class": "TupleType", "implicit": false, "items": ["PIL._imaging.ImagingCore", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getmetrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.FreeTypeFont.getmetrics", "name": "getmetrics", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL.ImageFont.FreeTypeFont"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getmetrics of FreeTypeFont", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.FreeTypeFont.getname", "name": "getname", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL.ImageFont.FreeTypeFont"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getname of FreeTypeFont", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageFont.FreeTypeFont.index", "name": "index", "type": "builtins.int"}}, "layout_engine": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageFont.FreeTypeFont.layout_engine", "name": "layout_engine", "type": "PIL.ImageFont.Layout"}}, "path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageFont.FreeTypeFont.path", "name": "path", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._typing.StrOrBytesPath"}, "typing.BinaryIO"], "uses_pep604_syntax": true}}}, "set_variation_by_axes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.FreeTypeFont.set_variation_by_axes", "name": "set_variation_by_axes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "axes"], "arg_types": ["PIL.ImageFont.FreeTypeFont", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_variation_by_axes of FreeTypeFont", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_variation_by_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.FreeTypeFont.set_variation_by_name", "name": "set_variation_by_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["PIL.ImageFont.FreeTypeFont", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_variation_by_name of FreeTypeFont", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageFont.FreeTypeFont.size", "name": "size", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PIL.ImageFont.FreeTypeFont.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PIL.ImageFont.FreeTypeFont", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "Image": {".class": "SymbolTableNode", "cross_ref": "PIL.Image", "kind": "Gdef"}, "ImageFile": {".class": "SymbolTableNode", "cross_ref": "PIL.ImageFile", "kind": "Gdef"}, "ImageFont": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL.ImageFont.ImageFont", "name": "ImageFont", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.ImageFont", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PIL.ImageFont", "mro": ["PIL.ImageFont.ImageFont", "builtins.object"], "names": {".class": "SymbolTable", "_load_pilfont": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.ImageFont._load_pilfont", "name": "_load_pilfont", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "filename"], "arg_types": ["PIL.ImageFont.ImageFont", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_pilfont of ImageFont", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_pilfont_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "file", "image"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.ImageFont._load_pilfont_data", "name": "_load_pilfont_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "file", "image"], "arg_types": ["PIL.ImageFont.ImageFont", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, "PIL.Image.Image"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_pilfont_data of ImageFont", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageFont.ImageFont.file", "name": "file", "type": "builtins.str"}}, "font": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "PIL.ImageFont.ImageFont.font", "name": "font", "type": "PIL._imaging.ImagingFont"}}, "getbbox": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "text", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.ImageFont.getbbox", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "text", "args", "kwargs"], "arg_types": ["PIL.ImageFont.ImageFont", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getbb<PERSON> of ImageFont", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getlength": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "text", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.ImageFont.getlength", "name": "getlength", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "text", "args", "kwargs"], "arg_types": ["PIL.ImageFont.ImageFont", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getlength of ImageFont", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getmask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 2, 4], "arg_names": ["self", "text", "mode", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.ImageFont.getmask", "name": "getmask", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 2, 4], "arg_names": ["self", "text", "mode", "args", "kwargs"], "arg_types": ["PIL.ImageFont.ImageFont", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getmask of ImageFont", "ret_type": "PIL._imaging.ImagingCore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageFont.ImageFont.info", "name": "info", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PIL.ImageFont.ImageFont.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PIL.ImageFont.ImageFont", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImagingFont": {".class": "SymbolTableNode", "cross_ref": "PIL._imaging.ImagingFont", "kind": "Gdef"}, "IntEnum": {".class": "SymbolTableNode", "cross_ref": "enum.IntEnum", "kind": "Gdef"}, "Layout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL.ImageFont.Layout", "name": "Layout", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "PIL.ImageFont.Layout", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "PIL.ImageFont", "mro": ["PIL.ImageFont.Layout", "enum.IntEnum", "builtins.int", "enum.ReprEnum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "BASIC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageFont.Layout.BASIC", "name": "BASIC", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "RAQM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageFont.Layout.RAQM", "name": "RAQM", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PIL.ImageFont.Layout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PIL.ImageFont.Layout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MAX_STRING_LENGTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageFont.MAX_STRING_LENGTH", "name": "MAX_STRING_LENGTH", "type": "builtins.int"}}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef"}, "StrOrBytesPath": {".class": "SymbolTableNode", "cross_ref": "PIL._typing.StrOrBytesPath", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageFont.TYPE_CHECKING", "name": "TYPE_CHECKING", "type": "builtins.bool"}}, "TransposedFont": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL.ImageFont.TransposedFont", "name": "TransposedFont", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.TransposedFont", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PIL.ImageFont", "mro": ["PIL.ImageFont.TransposedFont", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "font", "orientation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.TransposedFont.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "font", "orientation"], "arg_types": ["PIL.ImageFont.TransposedFont", {".class": "UnionType", "items": ["PIL.ImageFont.ImageFont", "PIL.ImageFont.FreeTypeFont"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["PIL.Image.Transpose", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TransposedFont", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "font": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageFont.TransposedFont.font", "name": "font", "type": {".class": "UnionType", "items": ["PIL.ImageFont.ImageFont", "PIL.ImageFont.FreeTypeFont"], "uses_pep604_syntax": false}}}, "getbbox": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "text", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.TransposedFont.getbbox", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "text", "args", "kwargs"], "arg_types": ["PIL.ImageFont.TransposedFont", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getbbox of TransposedFont", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getlength": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "text", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.TransposedFont.getlength", "name": "getlength", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "text", "args", "kwargs"], "arg_types": ["PIL.ImageFont.TransposedFont", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getlength of TransposedFont", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getmask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 2, 4], "arg_names": ["self", "text", "mode", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.TransposedFont.getmask", "name": "getmask", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 2, 4], "arg_names": ["self", "text", "mode", "args", "kwargs"], "arg_types": ["PIL.ImageFont.TransposedFont", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getmask of TransposedFont", "ret_type": "PIL._imaging.ImagingCore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "orientation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageFont.TransposedFont.orientation", "name": "orientation", "type": {".class": "UnionType", "items": ["PIL.Image.Transpose", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PIL.ImageFont.TransposedFont.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PIL.ImageFont.TransposedFont", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageFont.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageFont.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageFont.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageFont.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageFont.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageFont.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_string_length_check": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont._string_length_check", "name": "_string_length_check", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_string_length_check", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "base64": {".class": "SymbolTableNode", "cross_ref": "base64", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "core": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageFont.core", "name": "core", "type": {".class": "UnionType", "items": ["types.ModuleType", "PIL._util.DeferredError"], "uses_pep604_syntax": true}}}, "ex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageFont.ex", "name": "ex", "type": {".class": "DeletedType", "source": "ex"}}}, "features": {".class": "SymbolTableNode", "cross_ref": "PIL.features", "kind": "Gdef"}, "is_path": {".class": "SymbolTableNode", "cross_ref": "PIL._util.is_path", "kind": "Gdef"}, "load": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["filename"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load", "ret_type": "PIL.ImageFont.ImageFont", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.load_default", "name": "load_default", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["size"], "arg_types": [{".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_default", "ret_type": {".class": "UnionType", "items": ["PIL.ImageFont.FreeTypeFont", "PIL.ImageFont.ImageFont"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_default_imagefont": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.load_default_imagefont", "name": "load_default_imagefont", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_default_imagefont", "ret_type": "PIL.ImageFont.ImageFont", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.load_path", "name": "load_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["filename"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_path", "ret_type": "PIL.ImageFont.ImageFont", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "truetype": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["font", "size", "index", "encoding", "layout_engine"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageFont.truetype", "name": "truetype", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["font", "size", "index", "encoding", "layout_engine"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._typing.StrOrBytesPath"}, "typing.BinaryIO"], "uses_pep604_syntax": true}, "builtins.float", "builtins.int", "builtins.str", {".class": "UnionType", "items": ["PIL.ImageFont.Layout", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "truetype", "ret_type": "PIL.ImageFont.FreeTypeFont", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "/Users/<USER>/Developers/Tinkering/portfolio/.venv/lib/python3.11/site-packages/PIL/ImageFont.py"}