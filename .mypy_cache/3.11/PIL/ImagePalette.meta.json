{"data_mtime": 1753085613, "dep_lines": [21, 24, 24, 24, 24, 28, 18, 20, 22, 24, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 25, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "PIL.GimpGradientFile", "PIL.GimpPaletteFile", "PIL.ImageColor", "PIL.PaletteFile", "PIL.Image", "__future__", "array", "typing", "PIL", "builtins", "_frozen_importlib", "_typeshed", "abc", "types", "typing_extensions"], "hash": "96173733def723e6483215b1f5da41bab37a618a", "id": "PIL.ImagePalette", "ignore_all": true, "interface_hash": "a6799b6dac4ef6961ecad5513cda042a6b1ac73b", "mtime": 1753085295, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Developers/Tinkering/portfolio/.venv/lib/python3.11/site-packages/PIL/ImagePalette.py", "plugin_data": null, "size": 9009, "suppressed": [], "version_id": "1.15.0"}