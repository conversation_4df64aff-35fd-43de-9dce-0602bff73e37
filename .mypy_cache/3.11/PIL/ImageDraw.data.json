{".class": "MypyFile", "_fullname": "PIL.ImageDraw", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyStr": {".class": "SymbolTableNode", "cross_ref": "typing.AnyStr", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Coords": {".class": "SymbolTableNode", "cross_ref": "PIL._typing.Coords", "kind": "Gdef"}, "Draw": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["im", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.Draw", "name": "Draw", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["im", "mode"], "arg_types": ["PIL.Image.Image", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Draw", "ret_type": "PIL.ImageDraw.ImageDraw", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Image": {".class": "SymbolTableNode", "cross_ref": "PIL.Image", "kind": "Gdef"}, "ImageColor": {".class": "SymbolTableNode", "cross_ref": "PIL.ImageColor", "kind": "Gdef"}, "ImageDraw": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL.ImageDraw.ImageDraw", "name": "ImageDraw", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PIL.ImageDraw", "mro": ["PIL.ImageDraw.ImageDraw", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "im", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "im", "mode"], "arg_types": ["PIL.ImageDraw.ImageDraw", "PIL.Image.Image", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImageDraw", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_getfont": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "font_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw._getfont", "name": "_getfont", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "font_size"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_getfont of ImageDraw", "ret_type": {".class": "UnionType", "items": ["PIL.ImageFont.ImageFont", "PIL.ImageFont.FreeTypeFont", "PIL.ImageFont.TransposedFont"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_getink": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "ink", "fill"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw._getink", "name": "_getink", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "ink", "fill"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_getink of ImageDraw", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_image": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageDraw.ImageDraw._image", "name": "_image", "type": "PIL.Image.Image"}}, "_multiline_check": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw._multiline_check", "name": "_multiline_check", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "PIL.ImageDraw.ImageDraw._multiline_check", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_multiline_check of ImageDraw", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "PIL.ImageDraw.ImageDraw._multiline_check", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}, "_prepare_multiline_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "xy", "text", "font", "anchor", "spacing", "align", "direction", "features", "language", "stroke_width", "embedded_color", "font_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw._prepare_multiline_text", "name": "_prepare_multiline_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "xy", "text", "font", "anchor", "spacing", "align", "direction", "features", "language", "stroke_width", "embedded_color", "font_size"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "PIL.ImageDraw.ImageDraw._prepare_multiline_text", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "UnionType", "items": ["PIL.ImageFont.ImageFont", "PIL.ImageFont.FreeTypeFont", "PIL.ImageFont.TransposedFont", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_multiline_text of ImageDraw", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["PIL.ImageFont.ImageFont", "PIL.ImageFont.FreeTypeFont", "PIL.ImageFont.TransposedFont"], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "PIL.ImageDraw.ImageDraw._prepare_multiline_text", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "PIL.ImageDraw.ImageDraw._prepare_multiline_text", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}, "arc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "xy", "start", "end", "fill", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.arc", "name": "arc", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "xy", "start", "end", "fill", "width"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "TypeAliasType", "args": [], "type_ref": "PIL._typing.Coords"}, "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arc of ImageDraw", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bitmap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "xy", "bitmap", "fill"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.bitmap", "name": "bitmap", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "xy", "bitmap", "fill"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "PIL.Image.Image", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitmap of ImageDraw", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chord": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "xy", "start", "end", "fill", "outline", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.chord", "name": "chord", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "xy", "start", "end", "fill", "outline", "width"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "TypeAliasType", "args": [], "type_ref": "PIL._typing.Coords"}, "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chord of ImageDraw", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "circle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "xy", "radius", "fill", "outline", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.circle", "name": "circle", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "xy", "radius", "fill", "outline", "width"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.float", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "circle of ImageDraw", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageDraw.ImageDraw.draw", "name": "draw", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "ellipse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "xy", "fill", "outline", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.ellipse", "name": "ellipse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "xy", "fill", "outline", "width"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "TypeAliasType", "args": [], "type_ref": "PIL._typing.Coords"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ellipse of ImageDraw", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fill": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageDraw.ImageDraw.fill", "name": "fill", "type": "builtins.bool"}}, "font": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "PIL.ImageDraw.ImageDraw.font", "name": "font", "type": {".class": "UnionType", "items": ["PIL.ImageFont.ImageFont", "PIL.ImageFont.FreeTypeFont", "PIL.ImageFont.TransposedFont", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "fontmode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageDraw.ImageDraw.fontmode", "name": "fontmode", "type": "builtins.str"}}, "getfont": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.getfont", "name": "getfont", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL.ImageDraw.ImageDraw"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfont of ImageDraw", "ret_type": {".class": "UnionType", "items": ["PIL.ImageFont.ImageFont", "PIL.ImageFont.FreeTypeFont", "PIL.ImageFont.TransposedFont"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "im": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageDraw.ImageDraw.im", "name": "im", "type": "PIL._imaging.ImagingCore"}}, "ink": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageDraw.ImageDraw.ink", "name": "ink", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "xy", "fill", "width", "joint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.line", "name": "line", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "xy", "fill", "width", "joint"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "TypeAliasType", "args": [], "type_ref": "PIL._typing.Coords"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "line of ImageDraw", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageDraw.ImageDraw.mode", "name": "mode", "type": "builtins.str"}}, "multiline_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["self", "xy", "text", "fill", "font", "anchor", "spacing", "align", "direction", "features", "language", "stroke_width", "stroke_fill", "embedded_color", "font_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.multiline_text", "name": "multiline_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["self", "xy", "text", "fill", "font", "anchor", "spacing", "align", "direction", "features", "language", "stroke_width", "stroke_fill", "embedded_color", "font_size"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "PIL.ImageDraw.ImageDraw.multiline_text", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["PIL.ImageFont.ImageFont", "PIL.ImageFont.FreeTypeFont", "PIL.ImageFont.TransposedFont", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "multiline_text of ImageDraw", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "PIL.ImageDraw.ImageDraw.multiline_text", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}, "multiline_textbbox": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["self", "xy", "text", "font", "anchor", "spacing", "align", "direction", "features", "language", "stroke_width", "embedded_color", "font_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.multiline_textbbox", "name": "multiline_textbbox", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["self", "xy", "text", "font", "anchor", "spacing", "align", "direction", "features", "language", "stroke_width", "embedded_color", "font_size"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "PIL.ImageDraw.ImageDraw.multiline_textbbox", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "UnionType", "items": ["PIL.ImageFont.ImageFont", "PIL.ImageFont.FreeTypeFont", "PIL.ImageFont.TransposedFont", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "multiline_textbbox of ImageDraw", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "PIL.ImageDraw.ImageDraw.multiline_textbbox", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}, "palette": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageDraw.ImageDraw.palette", "name": "palette", "type": {".class": "UnionType", "items": ["PIL.ImagePalette.ImagePalette", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "pieslice": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "xy", "start", "end", "fill", "outline", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.pieslice", "name": "pieslice", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "xy", "start", "end", "fill", "outline", "width"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "TypeAliasType", "args": [], "type_ref": "PIL._typing.Coords"}, "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pieslice of ImageDraw", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "xy", "fill"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.point", "name": "point", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "xy", "fill"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "TypeAliasType", "args": [], "type_ref": "PIL._typing.Coords"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "point of ImageDraw", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "polygon": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "xy", "fill", "outline", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.polygon", "name": "polygon", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "xy", "fill", "outline", "width"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "TypeAliasType", "args": [], "type_ref": "PIL._typing.Coords"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "polygon of ImageDraw", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rectangle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "xy", "fill", "outline", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.rectangle", "name": "rectangle", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "xy", "fill", "outline", "width"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "TypeAliasType", "args": [], "type_ref": "PIL._typing.Coords"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rectangle of ImageDraw", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "regular_polygon": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "bounding_circle", "n_sides", "rotation", "fill", "outline", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.regular_polygon", "name": "regular_polygon", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "bounding_circle", "n_sides", "rotation", "fill", "outline", "width"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.float"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.float", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "regular_polygon of ImageDraw", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rounded_rectangle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 5], "arg_names": ["self", "xy", "radius", "fill", "outline", "width", "corners"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.rounded_rectangle", "name": "rounded_rectangle", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 5], "arg_names": ["self", "xy", "radius", "fill", "outline", "width", "corners"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "TypeAliasType", "args": [], "type_ref": "PIL._typing.Coords"}, "builtins.float", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rounded_rectangle of ImageDraw", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "shape", "fill", "outline"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.shape", "name": "shape", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "shape", "fill", "outline"], "arg_types": ["PIL.ImageDraw.ImageDraw", "PIL._imaging._Outline", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shape of ImageDraw", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 4], "arg_names": ["self", "xy", "text", "fill", "font", "anchor", "spacing", "align", "direction", "features", "language", "stroke_width", "stroke_fill", "embedded_color", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.text", "name": "text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 4], "arg_names": ["self", "xy", "text", "fill", "font", "anchor", "spacing", "align", "direction", "features", "language", "stroke_width", "stroke_fill", "embedded_color", "args", "kwargs"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "PIL.ImageDraw.ImageDraw.text", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["PIL.ImageFont.ImageFont", "PIL.ImageFont.FreeTypeFont", "PIL.ImageFont.TransposedFont", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageDraw._Ink"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "text of ImageDraw", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "PIL.ImageDraw.ImageDraw.text", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}, "textbbox": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["self", "xy", "text", "font", "anchor", "spacing", "align", "direction", "features", "language", "stroke_width", "embedded_color", "font_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.textbbox", "name": "textbbox", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["self", "xy", "text", "font", "anchor", "spacing", "align", "direction", "features", "language", "stroke_width", "embedded_color", "font_size"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "PIL.ImageDraw.ImageDraw.textbbox", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "UnionType", "items": ["PIL.ImageFont.ImageFont", "PIL.ImageFont.FreeTypeFont", "PIL.ImageFont.TransposedFont", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "textbbox of ImageDraw", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "PIL.ImageDraw.ImageDraw.textbbox", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}, "textlength": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 5], "arg_names": ["self", "text", "font", "direction", "features", "language", "embedded_color", "font_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.ImageDraw.textlength", "name": "textlength", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 5], "arg_names": ["self", "text", "font", "direction", "features", "language", "embedded_color", "font_size"], "arg_types": ["PIL.ImageDraw.ImageDraw", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "PIL.ImageDraw.ImageDraw.textlength", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "UnionType", "items": ["PIL.ImageFont.ImageFont", "PIL.ImageFont.FreeTypeFont", "PIL.ImageFont.TransposedFont", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "textlength of ImageDraw", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "PIL.ImageDraw.ImageDraw.textlength", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PIL.ImageDraw.ImageDraw.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PIL.ImageDraw.ImageDraw", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImageDraw2": {".class": "SymbolTableNode", "cross_ref": "PIL.ImageDraw2", "kind": "Gdef"}, "ImageFont": {".class": "SymbolTableNode", "cross_ref": "PIL.ImageFont", "kind": "Gdef"}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef"}, "Outline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "PIL.ImageDraw.Outline", "name": "Outline", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "PIL._imaging._Outline", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageDraw.TYPE_CHECKING", "name": "TYPE_CHECKING", "type": "builtins.bool"}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_Ink": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "PIL.ImageDraw._Ink", "line": 51, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.str"], "uses_pep604_syntax": false}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageDraw.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageDraw.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageDraw.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageDraw.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageDraw.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageDraw.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_color_diff": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["color1", "color2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw._color_diff", "name": "_color_diff", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["color1", "color2"], "arg_types": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_color_diff", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_regular_polygon_vertices": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["bounding_circle", "n_sides", "rotation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw._compute_regular_polygon_vertices", "name": "_compute_regular_polygon_vertices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["bounding_circle", "n_sides", "rotation"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.float"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compute_regular_polygon_vertices", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "deprecate": {".class": "SymbolTableNode", "cross_ref": "PIL._deprecate.deprecate", "kind": "Gdef"}, "floodfill": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["image", "xy", "value", "border", "thresh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.floodfill", "name": "floodfill", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["image", "xy", "value", "border", "thresh"], "arg_types": ["PIL.Image.Image", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "floodfill", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getdraw": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["im", "hints"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageDraw.getdraw", "name": "getdraw", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["im", "hints"], "arg_types": [{".class": "UnionType", "items": ["PIL.Image.Image", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getdraw", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["PIL.ImageDraw2.Draw", {".class": "NoneType"}], "uses_pep604_syntax": true}, "types.ModuleType"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "struct": {".class": "SymbolTableNode", "cross_ref": "struct", "kind": "Gdef"}}, "path": "/Users/<USER>/Developers/Tinkering/portfolio/.venv/lib/python3.11/site-packages/PIL/ImageDraw.py"}