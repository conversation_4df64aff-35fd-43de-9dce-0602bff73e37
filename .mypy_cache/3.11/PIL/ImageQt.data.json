{".class": "MypyFile", "_fullname": "PIL.ImageQt", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Image": {".class": "SymbolTableNode", "cross_ref": "PIL.Image", "kind": "Gdef"}, "ImageFile": {".class": "SymbolTableNode", "cross_ref": "PIL.ImageFile", "kind": "Gdef"}, "ImageQt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL.ImageQt.ImageQt", "name": "ImageQt", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "PIL.ImageQt.ImageQt", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PIL.ImageQt", "mro": ["PIL.ImageQt.ImageQt", "builtins.object"], "names": {".class": "SymbolTable", "__data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageQt.ImageQt.__data", "name": "__data", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "im"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageQt.ImageQt.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "im"], "arg_types": ["PIL.ImageQt.ImageQt", {".class": "UnionType", "items": ["PIL.Image.Image", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageQt.QByteArray"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImageQt", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PIL.ImageQt.ImageQt.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PIL.ImageQt.ImageQt", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PyQt6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "PIL.ImageQt.PyQt6", "name": "PyQt6", "type": {".class": "AnyType", "missing_import_name": "PIL.ImageQt.PyQt6", "source_any": null, "type_of_any": 3}}}, "PySide6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "PIL.ImageQt.PySide6", "name": "PySide6", "type": {".class": "AnyType", "missing_import_name": "PIL.ImageQt.PySide6", "source_any": null, "type_of_any": 3}}}, "QBuffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageQt.QBuffer", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "builtins.type"}}, "QByteArray": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "PIL.ImageQt.QByteArray", "line": 35, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "PIL.ImageQt.PyQt6", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "PIL.ImageQt.PySide6", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}}}, "QIODevice": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "PIL.ImageQt.QIODevice", "line": 36, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "PIL.ImageQt.PyQt6", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "PIL.ImageQt.PySide6", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}}}, "QImage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "PIL.ImageQt.QImage", "line": 37, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "PIL.ImageQt.PyQt6", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "PIL.ImageQt.PySide6", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}}}, "QPixmap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "PIL.ImageQt.QPixmap", "line": 38, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "PIL.ImageQt.PyQt6", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "PIL.ImageQt.PySide6", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageQt.TYPE_CHECKING", "name": "TYPE_CHECKING", "type": "builtins.bool"}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageQt.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageQt.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageQt.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageQt.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageQt.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageQt.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_toqclass_helper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["im"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageQt._toqclass_helper", "name": "_toqclass_helper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["im"], "arg_types": [{".class": "UnionType", "items": ["PIL.Image.Image", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageQt.QByteArray"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_toqclass_helper", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "align8to32": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["bytes", "width", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageQt.align8to32", "name": "align8to32", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["bytes", "width", "mode"], "arg_types": ["builtins.bytes", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "align8to32", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "fromqimage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["im"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageQt.fromqimage", "name": "fromqimage", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["im"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageQt.QImage"}, {".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageQt.QPixmap"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fromqimage", "ret_type": "PIL.ImageFile.ImageFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fromqpixmap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["im"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageQt.fromqpixmap", "name": "fromqpixmap", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["im"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageQt.QPixmap"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fromqpixmap", "ret_type": "PIL.ImageFile.ImageFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_path": {".class": "SymbolTableNode", "cross_ref": "PIL._util.is_path", "kind": "Gdef"}, "qRgba": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageQt.qRgba", "name": "qRgba", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "qt_is_installed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageQt.qt_is_installed", "name": "qt_is_installed", "type": "builtins.bool"}}, "qt_module": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "PIL.ImageQt.qt_module", "name": "qt_module", "type": "builtins.str"}}, "qt_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageQt.qt_version", "name": "qt_version", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "qt_versions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "PIL.ImageQt.qt_versions", "name": "qt_versions", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "rgb": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["r", "g", "b", "a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageQt.rgb", "name": "rgb", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["r", "g", "b", "a"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rgb", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "toqimage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["im"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageQt.toqimage", "name": "toqimage", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["im"], "arg_types": [{".class": "UnionType", "items": ["PIL.Image.Image", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageQt.QByteArray"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "toqimage", "ret_type": "PIL.ImageQt.ImageQt", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "toqpixmap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["im"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageQt.toqpixmap", "name": "toqpixmap", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["im"], "arg_types": [{".class": "UnionType", "items": ["PIL.Image.Image", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageQt.QByteArray"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "toqpixmap", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageQt.QPixmap"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "PIL.ImageQt.version", "name": "version", "type": "builtins.str"}}}, "path": "/Users/<USER>/Developers/Tinkering/portfolio/.venv/lib/python3.11/site-packages/PIL/ImageQt.py"}