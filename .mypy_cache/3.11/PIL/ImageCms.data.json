{".class": "MypyFile", "_fullname": "PIL.ImageCms", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "DeferredError": {".class": "SymbolTableNode", "cross_ref": "PIL._util.DeferredError", "kind": "Gdef"}, "Direction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL.ImageCms.Direction", "name": "Direction", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "PIL.ImageCms.Direction", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "PIL.ImageCms", "mro": ["PIL.ImageCms.Direction", "enum.IntEnum", "builtins.int", "enum.ReprEnum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "INPUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Direction.INPUT", "name": "INPUT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "OUTPUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Direction.OUTPUT", "name": "OUTPUT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "PROOF": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Direction.PROOF", "name": "PROOF", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PIL.ImageCms.Direction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PIL.ImageCms.Direction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Flags": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntFlag"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL.ImageCms.Flags", "name": "Flags", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "PIL.ImageCms.Flags", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "PIL.ImageCms", "mro": ["PIL.ImageCms.Flags", "enum.IntFlag", "builtins.int", "enum.ReprEnum", "enum.Flag", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "BLACKPOINTCOMPENSATION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.BLACKPOINTCOMPENSATION", "name": "BLACKPOINTCOMPENSATION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8192}, "type_ref": "builtins.int"}}}, "CLUT_POST_LINEARIZATION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.CLUT_POST_LINEARIZATION", "name": "CLUT_POST_LINEARIZATION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "CLUT_PRE_LINEARIZATION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.CLUT_PRE_LINEARIZATION", "name": "CLUT_PRE_LINEARIZATION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16}, "type_ref": "builtins.int"}}}, "COPY_ALPHA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.COPY_ALPHA", "name": "COPY_ALPHA", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 67108864}, "type_ref": "builtins.int"}}}, "FORCE_CLUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.FORCE_CLUT", "name": "FORCE_CLUT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "GAMUTCHECK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.GAMUTCHECK", "name": "GAMUTCHECK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4096}, "type_ref": "builtins.int"}}}, "GRIDPOINTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["n"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "PIL.ImageCms.Flags.GRIDPOINTS", "name": "GRIDPOINTS", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["n"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "GRIDPOINTS of Flags", "ret_type": "PIL.ImageCms.Flags", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "PIL.ImageCms.Flags.GRIDPOINTS", "name": "GRIDPOINTS", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["n"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "GRIDPOINTS of Flags", "ret_type": "PIL.ImageCms.Flags", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "GUESSDEVICECLASS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.GUESSDEVICECLASS", "name": "GUESSDEVICECLASS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32}, "type_ref": "builtins.int"}}}, "HIGHRESPRECALC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.HIGHRESPRECALC", "name": "HIGHRESPRECALC", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1024}, "type_ref": "builtins.int"}}}, "KEEP_SEQUENCE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.KEEP_SEQUENCE", "name": "KEEP_SEQUENCE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 128}, "type_ref": "builtins.int"}}}, "LOWRESPRECALC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.LOWRESPRECALC", "name": "LOWRESPRECALC", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2048}, "type_ref": "builtins.int"}}}, "NOCACHE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.NOCACHE", "name": "NOCACHE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 64}, "type_ref": "builtins.int"}}}, "NODEFAULTRESOURCEDEF": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.NODEFAULTRESOURCEDEF", "name": "NODEFAULTRESOURCEDEF", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16777216}, "type_ref": "builtins.int"}}}, "NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.NONE", "name": "NONE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "NONEGATIVES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.NONEGATIVES", "name": "NONEGATIVES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32768}, "type_ref": "builtins.int"}}}, "NOOPTIMIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.NOOPTIMIZE", "name": "NOOPTIMIZE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 256}, "type_ref": "builtins.int"}}}, "NOWHITEONWHITEFIXUP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.NOWHITEONWHITEFIXUP", "name": "NOWHITEONWHITEFIXUP", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "NULLTRANSFORM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.NULLTRANSFORM", "name": "NULLTRANSFORM", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 512}, "type_ref": "builtins.int"}}}, "SOFTPROOFING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.SOFTPROOFING", "name": "SOFTPROOFING", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16384}, "type_ref": "builtins.int"}}}, "USE_8BITS_DEVICELINK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags.USE_8BITS_DEVICELINK", "name": "USE_8BITS_DEVICELINK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "_GRIDPOINTS_1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags._GRIDPOINTS_1", "name": "_GRIDPOINTS_1", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 65536}, "type_ref": "builtins.int"}}}, "_GRIDPOINTS_128": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags._GRIDPOINTS_128", "name": "_GRIDPOINTS_128", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8388608}, "type_ref": "builtins.int"}}}, "_GRIDPOINTS_16": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags._GRIDPOINTS_16", "name": "_GRIDPOINTS_16", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1048576}, "type_ref": "builtins.int"}}}, "_GRIDPOINTS_2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags._GRIDPOINTS_2", "name": "_GRIDPOINTS_2", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 131072}, "type_ref": "builtins.int"}}}, "_GRIDPOINTS_32": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags._GRIDPOINTS_32", "name": "_GRIDPOINTS_32", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2097152}, "type_ref": "builtins.int"}}}, "_GRIDPOINTS_4": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags._GRIDPOINTS_4", "name": "_GRIDPOINTS_4", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 262144}, "type_ref": "builtins.int"}}}, "_GRIDPOINTS_64": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags._GRIDPOINTS_64", "name": "_GRIDPOINTS_64", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4194304}, "type_ref": "builtins.int"}}}, "_GRIDPOINTS_8": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Flags._GRIDPOINTS_8", "name": "_GRIDPOINTS_8", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 524288}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PIL.ImageCms.Flags.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PIL.ImageCms.Flags", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Image": {".class": "SymbolTableNode", "cross_ref": "PIL.Image", "kind": "Gdef"}, "ImageCmsProfile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL.ImageCms.ImageCmsProfile", "name": "ImageCmsProfile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.ImageCmsProfile", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PIL.ImageCms", "mro": ["PIL.ImageCms.ImageCmsProfile", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "profile"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.ImageCmsProfile.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "profile"], "arg_types": ["PIL.ImageCms.ImageCmsProfile", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "PIL._typing.SupportsRead"}, "PIL._imagingcms.CmsProfile"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImageCmsProfile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filename": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageCms.ImageCmsProfile.filename", "name": "filename", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "product_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageCms.ImageCmsProfile.product_info", "name": "product_info", "type": {".class": "NoneType"}}}, "product_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageCms.ImageCmsProfile.product_name", "name": "product_name", "type": {".class": "NoneType"}}}, "profile": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageCms.ImageCmsProfile.profile", "name": "profile", "type": "PIL._imagingcms.CmsProfile"}}, "tobytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.ImageCmsProfile.tobytes", "name": "tobytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL.ImageCms.ImageCmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tobytes of ImageCmsProfile", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PIL.ImageCms.ImageCmsProfile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PIL.ImageCms.ImageCmsProfile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImageCmsTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["PIL.Image.ImagePointHandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL.ImageCms.ImageCmsTransform", "name": "ImageCmsTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.ImageCmsTransform", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "PIL.ImageCms", "mro": ["PIL.ImageCms.ImageCmsTransform", "PIL.Image.ImagePointHandler", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "input", "output", "input_mode", "output_mode", "intent", "proof", "proof_intent", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.ImageCmsTransform.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "input", "output", "input_mode", "output_mode", "intent", "proof", "proof_intent", "flags"], "arg_types": ["PIL.ImageCms.ImageCmsTransform", "PIL.ImageCms.ImageCmsProfile", "PIL.ImageCms.ImageCmsProfile", "builtins.str", "builtins.str", "PIL.ImageCms.Intent", {".class": "UnionType", "items": ["PIL.ImageCms.ImageCmsProfile", {".class": "NoneType"}], "uses_pep604_syntax": true}, "PIL.ImageCms.Intent", "PIL.ImageCms.Flags"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImageCmsTransform", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "im", "imOut"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.ImageCmsTransform.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "im", "imOut"], "arg_types": ["PIL.ImageCms.ImageCmsTransform", "PIL.Image.Image", {".class": "UnionType", "items": ["PIL.Image.Image", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of ImageCmsTransform", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_in_place": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "im"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.ImageCmsTransform.apply_in_place", "name": "apply_in_place", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "im"], "arg_types": ["PIL.ImageCms.ImageCmsTransform", "PIL.Image.Image"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_in_place of ImageCmsTransform", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inputMode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageCms.ImageCmsTransform.inputMode", "name": "inputMode", "type": "builtins.str"}}, "input_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageCms.ImageCmsTransform.input_mode", "name": "input_mode", "type": "builtins.str"}}, "outputMode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageCms.ImageCmsTransform.outputMode", "name": "outputMode", "type": "builtins.str"}}, "output_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageCms.ImageCmsTransform.output_mode", "name": "output_mode", "type": "builtins.str"}}, "output_profile": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageCms.ImageCmsTransform.output_profile", "name": "output_profile", "type": "PIL.ImageCms.ImageCmsProfile"}}, "point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "im"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.ImageCmsTransform.point", "name": "point", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "im"], "arg_types": ["PIL.ImageCms.ImageCmsTransform", "PIL.Image.Image"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "point of ImageCmsTransform", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transform": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageCms.ImageCmsTransform.transform", "name": "transform", "type": "PIL._imagingcms.CmsTransform"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PIL.ImageCms.ImageCmsTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PIL.ImageCms.ImageCmsTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IntEnum": {".class": "SymbolTableNode", "cross_ref": "enum.IntEnum", "kind": "Gdef"}, "IntFlag": {".class": "SymbolTableNode", "cross_ref": "enum.IntFlag", "kind": "Gdef"}, "Intent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL.ImageCms.Intent", "name": "Intent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "PIL.ImageCms.Intent", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "PIL.ImageCms", "mro": ["PIL.ImageCms.Intent", "enum.IntEnum", "builtins.int", "enum.ReprEnum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "ABSOLUTE_COLORIMETRIC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Intent.ABSOLUTE_COLORIMETRIC", "name": "ABSOLUTE_COLORIMETRIC", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "PERCEPTUAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Intent.PERCEPTUAL", "name": "PERCEPTUAL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "RELATIVE_COLORIMETRIC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Intent.RELATIVE_COLORIMETRIC", "name": "RELATIVE_COLORIMETRIC", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "SATURATION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.Intent.SATURATION", "name": "SATURATION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PIL.ImageCms.Intent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PIL.ImageCms.Intent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "PyCMSError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL.ImageCms.PyCMSError", "name": "PyCMSError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.PyCMSError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PIL.ImageCms", "mro": ["PIL.ImageCms.PyCMSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PIL.ImageCms.PyCMSError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PIL.ImageCms.PyCMSError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SupportsFloat": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsFloat", "kind": "Gdef"}, "SupportsInt": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsInt", "kind": "Gdef"}, "SupportsRead": {".class": "SymbolTableNode", "cross_ref": "PIL._typing.SupportsRead", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_CmsProfileCompatible": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "PIL.ImageCms._CmsProfileCompatible", "line": 35, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "PIL._typing.SupportsRead"}, "PIL._imagingcms.CmsProfile", "PIL.ImageCms.ImageCmsProfile"], "uses_pep604_syntax": false}}}, "_DESCRIPTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms._DESCRIPTION", "name": "_DESCRIPTION", "type": "builtins.str"}}, "_FLAGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms._FLAGS", "name": "_FLAGS", "type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_MAX_FLAG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms._MAX_FLAG", "name": "_MAX_FLAG", "type": "builtins.object"}}, "_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms._VERSION", "name": "_VERSION", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageCms.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageCms.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageCms.__file__", "name": "__file__", "type": "builtins.str"}}, "__getattr__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageCms.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageCms.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageCms.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "PIL.__version__", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "applyTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["im", "transform", "inPlace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.applyTransform", "name": "applyTransform", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["im", "transform", "inPlace"], "arg_types": ["PIL.Image.Image", "PIL.ImageCms.ImageCmsTransform", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "applyTransform", "ret_type": {".class": "UnionType", "items": ["PIL.Image.Image", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "buildProofTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["inputProfile", "outputProfile", "proofProfile", "inMode", "outMode", "renderingIntent", "proofRenderingIntent", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.buildProofTransform", "name": "buildProofTransform", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["inputProfile", "outputProfile", "proofProfile", "inMode", "outMode", "renderingIntent", "proofRenderingIntent", "flags"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}, {".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}, {".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}, "builtins.str", "builtins.str", "PIL.ImageCms.Intent", "PIL.ImageCms.Intent", "PIL.ImageCms.Flags"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "buildProofTransform", "ret_type": "PIL.ImageCms.ImageCmsTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "buildProofTransformFromOpenProfiles": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.buildProofTransformFromOpenProfiles", "name": "buildProofTransformFromOpenProfiles", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["inputProfile", "outputProfile", "proofProfile", "inMode", "outMode", "renderingIntent", "proofRenderingIntent", "flags"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}, {".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}, {".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}, "builtins.str", "builtins.str", "PIL.ImageCms.Intent", "PIL.ImageCms.Intent", "PIL.ImageCms.Flags"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "PIL.ImageCms.ImageCmsTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "buildTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["inputProfile", "outputProfile", "inMode", "outMode", "renderingIntent", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.buildTransform", "name": "buildTransform", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["inputProfile", "outputProfile", "inMode", "outMode", "renderingIntent", "flags"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}, {".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}, "builtins.str", "builtins.str", "PIL.ImageCms.Intent", "PIL.ImageCms.Flags"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "buildTransform", "ret_type": "PIL.ImageCms.ImageCmsTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "buildTransformFromOpenProfiles": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "PIL.ImageCms.buildTransformFromOpenProfiles", "name": "buildTransformFromOpenProfiles", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["inputProfile", "outputProfile", "inMode", "outMode", "renderingIntent", "flags"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}, {".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}, "builtins.str", "builtins.str", "PIL.ImageCms.Intent", "PIL.ImageCms.Flags"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "PIL.ImageCms.ImageCmsTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "core": {".class": "SymbolTableNode", "cross_ref": "PIL._imagingcms", "kind": "Gdef"}, "createProfile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["colorSpace", "colorTemp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.createProfile", "name": "createProfile", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["colorSpace", "colorTemp"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "LAB"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "XYZ"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sRGB"}], "uses_pep604_syntax": false}, "typing.SupportsFloat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createProfile", "ret_type": "PIL._imagingcms.CmsProfile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deprecate": {".class": "SymbolTableNode", "cross_ref": "PIL._deprecate.deprecate", "kind": "Gdef"}, "ex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.ImageCms.ex", "name": "ex", "type": {".class": "DeletedType", "source": "ex"}}}, "getDefaultIntent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["profile"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.getDefaultIntent", "name": "getDefaultIntent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["profile"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getDefaultIntent", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getOpenProfile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["profileFilename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.getOpenProfile", "name": "getOpenProfile", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["profileFilename"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "PIL._typing.SupportsRead"}, "PIL._imagingcms.CmsProfile"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getOpenProfile", "ret_type": "PIL.ImageCms.ImageCmsProfile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getProfileCopyright": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["profile"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.getProfileCopyright", "name": "getProfileCopyright", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["profile"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getProfileCopyright", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getProfileDescription": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["profile"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.getProfileDescription", "name": "getProfileDescription", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["profile"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getProfileDescription", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getProfileInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["profile"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.getProfileInfo", "name": "getProfileInfo", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["profile"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getProfileInfo", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getProfileManufacturer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["profile"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.getProfileManufacturer", "name": "getProfileManufacturer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["profile"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getProfileManufacturer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getProfileModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["profile"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.getProfileModel", "name": "getProfileModel", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["profile"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getProfileModel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getProfileName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["profile"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.getProfileName", "name": "getProfileName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["profile"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getProfileName", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_display_profile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.get_display_profile", "name": "get_display_profile", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["handle"], "arg_types": [{".class": "UnionType", "items": ["typing.SupportsInt", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_display_profile", "ret_type": {".class": "UnionType", "items": ["PIL.ImageCms.ImageCmsProfile", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isIntentSupported": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["profile", "intent", "direction"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.isIntentSupported", "name": "isIntentSupported", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["profile", "intent", "direction"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}, "PIL.ImageCms.Intent", "PIL.ImageCms.Direction"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isIntentSupported", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "profileToProfile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["im", "inputProfile", "outputProfile", "renderingIntent", "outputMode", "inPlace", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.profileToProfile", "name": "profileToProfile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["im", "inputProfile", "outputProfile", "renderingIntent", "outputMode", "inPlace", "flags"], "arg_types": ["PIL.Image.Image", {".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}, {".class": "TypeAliasType", "args": [], "type_ref": "PIL.ImageCms._CmsProfileCompatible"}, "PIL.ImageCms.Intent", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "PIL.ImageCms.Flags"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "profileToProfile", "ret_type": {".class": "UnionType", "items": ["PIL.Image.Image", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reduce": {".class": "SymbolTableNode", "cross_ref": "functools.reduce", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "versions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageCms.versions", "name": "versions", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "versions", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/Developers/Tinkering/portfolio/.venv/lib/python3.11/site-packages/PIL/ImageCms.py"}