{"data_mtime": 1753085604, "dep_lines": [5, 1, 3, 4, 6, 10, 1, 1, 1, 1, 1, 13, 13], "dep_prios": [5, 5, 10, 10, 5, 25, 5, 30, 30, 30, 30, 25, 25], "dependencies": ["collections.abc", "__future__", "os", "sys", "typing", "numbers", "builtins", "_frozen_importlib", "_typeshed", "abc", "types"], "hash": "846b018315d5f9b308d7bc0339ccbe08340cf161", "id": "PIL._typing", "ignore_all": true, "interface_hash": "f0bdbe8999c2ea4b8614bab4645713be622936b3", "mtime": 1753085295, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Developers/Tinkering/portfolio/.venv/lib/python3.11/site-packages/PIL/_typing.py", "plugin_data": null, "size": 1251, "suppressed": ["numpy.typing", "numpy"], "version_id": "1.15.0"}